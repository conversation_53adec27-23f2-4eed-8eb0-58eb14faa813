.airline-search-box {
  //box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  .search-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f7f8fa;
    border-radius: 8px;
    padding: 8px;
    margin-bottom: 12px;
    box-sizing: border-box;

    &:last-child {
      margin-bottom: 0;
    }
    .desc {
      font-size: 12px;
      color: #b5baca;
    }

    .city-name {
      font-size: 16px;
      font-weight: bold;
      color: #828998;
      margin-top: 4px;
    }
    .active {
      color: #2b2b32;
    }
    .calendar-icon {
      width: 16px;
      height: 16px;
    }
  }

  // 多程航线样式
  .multipass-segment {
    flex-direction: column;
    align-items: flex-start;
    background: #f7f8fa;
    margin-bottom: 12px;
    padding: 12px;

    .segment-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      margin-bottom: 8px;

      .segment-number {
        font-size: 14px;
        font-weight: bold;
        color: #124072;
      }

      .remove-segment-btn {
        padding: 0;
        background: transparent;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .desc {
      display: none;
    }
    .search-item {
      margin-bottom: 4px;
    }
  }
  .add-segment-text {
    font-size: 14px;
    color: #124072;
    padding-left: 4px;
  }

  .search-btn {
    background-color: #124072;
    color: #ffffff;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    line-height: 48px;
    border-radius: 12px;
    width: 100%;
    margin-top: 24px;
    border: none;
    outline: none;
    cursor: pointer;

    &:active {
      background-color: #124072;
    }
  }
}
