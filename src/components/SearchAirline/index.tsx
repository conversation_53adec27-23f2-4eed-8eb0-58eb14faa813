import { memo, useEffect, useState } from 'react';
import { Button, Image, View, Text } from '@tarojs/components';
import { ArrowTransfer, Plus, Minus } from '@nutui/icons-react-taro';
import './index.less';
import { Cascader, CascaderOption } from '@nutui/nutui-react-taro';
import { calendarIcon } from '@/utils/img';
import CustomCalendar from '@/components/CustomCalendar';
import CustomTag from '@/components/CustomTag';
import dayjs from 'dayjs';
import { DATE_FORMAT } from '@/utils/constant';

interface FlightSegment {
  departureCity: string;
  arrivalCity: string;
  date: string;
}

interface SearchAirlineProps {
  type?: 'oneWay' | 'roundTrip' | 'multipass';
  getData?: (data: FlightSegment[]) => void;
}
const SearchAirline = ({ type = 'oneWay', getData }: SearchAirlineProps) => {
  const [options, setOptions] = useState<CascaderOption[]>([]);
  const [depVal, setDepVal] = useState(''); //出发城市
  const [arrVal, setArrVal] = useState(''); //到达城市
  const [dateVal, setDateVal] = useState<string>(dayjs().format(DATE_FORMAT)); //航班日期
  const [returnDateVal, setReturnDateVal] = useState<string>(
    dayjs().add(2, 'day').format(DATE_FORMAT)
  ); //返程日期
  const [dateShow, setDateShow] = useState(''); //日期选择框是否显示
  const [currentSegment, setCurrentSegment] = useState(0); //当前编辑的航段

  // 多程航线数据
  const [multipassSegments, setMultipassSegments] = useState<FlightSegment[]>([
    { departureCity: '', arrivalCity: '', date: dayjs().format(DATE_FORMAT) },
    {
      departureCity: '',
      arrivalCity: '',
      date: dayjs().add(1, 'day').format(DATE_FORMAT),
    },
  ]);

  useEffect(() => {
    setTimeout(() => {
      setOptions([
        {
          value: '浙江',
          text: '浙江',
          children: [
            {
              value: '杭州',
              text: '杭州',
            },
            {
              value: '温州',
              text: '温州',
            },
          ],
        },
        {
          value: '湖南',
          text: '湖南',
          children: [
            {
              value: '长沙',
              text: '长沙',
            },
            {
              value: '岳阳',
              text: '岳阳',
            },
          ],
        },
        {
          value: '福建',
          text: '福建',
          children: [
            {
              value: '福州',
              text: '福州',
            },
          ],
        },
      ]);
    }, 300);
  }, []);
  //渲染地址
  const renderAddress = (val: string, setVal: any, segmentIndex?: number, isArrival?: boolean) => {
    const [visible, setVisible] = useState(false);
    const onChange = (value: any) => {
      console.log(value);
      if (segmentIndex !== undefined && type === 'multipass') {
        // 更新多程航线的城市
        setMultipassSegments(prev => {
          const newSegments = [...prev];
          if (isArrival) {
            newSegments[segmentIndex].arrivalCity = value?.[1] || '';
          } else {
            newSegments[segmentIndex].departureCity = value?.[1] || '';
          }
          return newSegments;
        });
      } else {
        setVal(value?.[1]);
      }
    };

    // 获取显示的值
    const displayVal =
      segmentIndex !== undefined && type === 'multipass'
        ? isArrival
          ? multipassSegments[segmentIndex].arrivalCity
          : multipassSegments[segmentIndex].departureCity
        : val;

    return (
      <>
        <View
          className={`city-name ${displayVal ? 'active' : ''}`}
          onClick={() => setVisible(true)}
        >
          {displayVal ? displayVal : '请选择'}
        </View>
        <Cascader
          visible={visible}
          title='选择地址'
          options={options}
          closeable
          onClose={() => {
            setVisible(false);
          }}
          onChange={onChange}
        />
      </>
    );
  };
  //交换出发城市和到达城市
  const exchangeCity = (segmentIndex?: number) => {
    if (segmentIndex !== undefined && type === 'multipass') {
      // 交换多程航线的城市
      setMultipassSegments(prev => {
        const newSegments = [...prev];
        const temp = newSegments[segmentIndex].departureCity;
        newSegments[segmentIndex].departureCity = newSegments[segmentIndex].arrivalCity;
        newSegments[segmentIndex].arrivalCity = temp;
        return newSegments;
      });
    } else {
      setDepVal(arrVal);
      setArrVal(depVal);
    }
  };

  // 添加航段
  const addSegment = () => {
    if (multipassSegments.length < 5) {
      setMultipassSegments(prev => [
        ...prev,
        { departureCity: '', arrivalCity: '', date: dayjs().format(DATE_FORMAT) },
      ]);
    }
  };

  // 删除航段
  const removeSegment = (index: number) => {
    if (multipassSegments.length > 2) {
      setMultipassSegments(prev => prev.filter((_, i) => i !== index));
    }
  };

  // 更新日期
  const updateDate = (date: string, segmentIndex?: number) => {
    if (segmentIndex !== undefined && type === 'multipass') {
      setMultipassSegments(prev => {
        const newSegments = [...prev];
        newSegments[segmentIndex].date = date;
        return newSegments;
      });
    } else if (type === 'roundTrip' && segmentIndex === 1) {
      setReturnDateVal(date);
    } else {
      setDateVal(date);
    }
  };

  // 准备提交数据
  const prepareData = () => {
    let segments: FlightSegment[] = [];

    if (type === 'oneWay') {
      segments = [
        {
          departureCity: depVal,
          arrivalCity: arrVal,
          date: dateVal,
        },
      ];
    } else if (type === 'roundTrip') {
      segments = [
        {
          departureCity: depVal,
          arrivalCity: arrVal,
          date: dateVal,
        },
        {
          departureCity: arrVal,
          arrivalCity: depVal,
          date: returnDateVal,
        },
      ];
    } else if (type === 'multipass') {
      segments = multipassSegments.filter(
        segment => segment.departureCity && segment.arrivalCity && segment.date
      );
    }

    return segments;
  };

  // 渲染城市选择框
  const renderCitySelector = (segmentIndex?: number) => {
    // 如果是多程航线，显示序号
    // const showHeader = type === 'multipass' && segmentIndex !== undefined;
    // // 判断是否可以删除航段
    // const canRemove =
    //   type === 'multipass' && multipassSegments.length > 2 && segmentIndex !== undefined;

    return (
      <View className={`search-item`}>
        <View className={'city-select dep-city'}>
          <View className={'desc'}>出发城市</View>
          {type === 'multipass' && segmentIndex !== undefined
            ? renderAddress('', null, segmentIndex, false)
            : renderAddress(depVal, setDepVal)}
        </View>
        <View
          className={'exchange-icon'}
          onClick={() => (segmentIndex !== undefined ? exchangeCity(segmentIndex) : exchangeCity())}
        >
          <ArrowTransfer width={12} height={12} color={'#B5BACA'} />
        </View>
        <View className={'city-select arr-city'}>
          <View className={'desc'}>到达城市</View>
          {type === 'multipass' && segmentIndex !== undefined
            ? renderAddress('', null, segmentIndex, true)
            : renderAddress(arrVal, setArrVal)}
        </View>
      </View>
    );
  };

  // 日期格式已修改为YYYY-MM-DD

  // 渲染日期选择框
  const renderDateSelector = (segmentIndex?: number) => {
    // 获取显示的日期值
    const dateValue =
      segmentIndex !== undefined && type === 'multipass'
        ? multipassSegments[segmentIndex].date
        : dateVal;

    // 点击日期选择框的处理函数
    const handleDateClick = () => {
      if (segmentIndex !== undefined) {
        setCurrentSegment(segmentIndex);
        setDateShow('multipass');
      } else {
        setDateShow('oneWay');
      }
    };

    return (
      <View className={'search-item time-select-box'}>
        <View className={'city-select dep-city'} onClick={handleDateClick}>
          <View className={'desc'}>航班日期</View>
          <View className={'val'}>{dateValue}</View>
        </View>
        <View className={'city-select arr-city'}>
          <Image src={calendarIcon} className={'calendar-icon'} />
        </View>
      </View>
    );
  };

  // 渲染往返航线的日期选择框
  const renderRoundTripDateSelector = () => {
    // 计算日期间隔
    const calculateDateInterval = () => {
      try {
        const depDate = dayjs(dateVal);
        const retDate = dayjs(returnDateVal);
        if (depDate.isValid() && retDate.isValid()) {
          const diffDays = retDate.diff(depDate, 'day');
          return `${Math.abs(diffDays)}天`;
        }
      } catch (e) {}
      return '0天';
    };

    return (
      <View className={'search-item time-select-box'}>
        <View
          className={'city-select dep-city'}
          onClick={() => {
            setCurrentSegment(0);
            setDateShow('roundTrip');
          }}
        >
          <View className={'desc'}>去程</View>
          <View className={'val'}>{dateVal}</View>
        </View>
        <View className={'interval-time'}>{calculateDateInterval()}</View>
        <View
          className={'city-select arr-city'}
          onClick={() => {
            setCurrentSegment(1);
            setDateShow('roundTrip');
          }}
        >
          <View className={'desc'}>返程</View>
          <View className={'val'}>{returnDateVal}</View>
        </View>
      </View>
    );
  };

  return (
    <>
      <View className={'airline-search-box'}>
        {/* 单程和往返航线的城市选择框 */}
        {(type === 'oneWay' || type === 'roundTrip') && renderCitySelector()}
        {/* 单程航线的日期选择框 */}
        {type === 'oneWay' && renderDateSelector()}
        {/* 往返航线的日期选择框 */}
        {type === 'roundTrip' && renderRoundTripDateSelector()}
        {type === 'multipass' && (
          <>
            {multipassSegments.map((_item, index) => (
              <View className={'card-box multipass-segment'} key={index}>
                <View className={'segment-header'}>
                  <Text className={'segment-number'}>航段 {index! + 1}</Text>
                  {multipassSegments.length > 2 && (
                    <Button className={'remove-segment-btn'} onClick={() => removeSegment(index!)}>
                      <Minus width={16} height={16} color={'#B5BACA'} />
                    </Button>
                  )}
                </View>
                {renderCitySelector(index)}
                {renderDateSelector(index)}
              </View>
            ))}
            {multipassSegments.length < 5 && (
              <CustomTag
                bgColor={'#F7F8FA'}
                color={'#124072'}
                onClick={() => addSegment()}
                size={'large'}
              >
                <Plus width={12} height={12} color={'#124072'} />
                <Text className={'add-segment-text'}>新增行程</Text>
              </CustomTag>
            )}
          </>
        )}
        <Button
          className={'search-btn'}
          onClick={() => {
            const segments = prepareData();
            getData?.(segments);
          }}
        >
          查询价格
        </Button>
      </View>
      <CustomCalendar
        customActionSheetProps={{
          visible: !!dateShow,
          onCancel: () => setDateShow(''),
          onConfirm: val => {
            if (!val) return;
            const selectedDate = val as string;
            if (dateShow === 'oneWay') {
              setDateVal(selectedDate);
            } else if (dateShow === 'roundTrip') {
              // 处理往返日期选择
              if (currentSegment === 0) {
                setDateVal(selectedDate);
              } else {
                setReturnDateVal(selectedDate);
              }
            } else if (dateShow === 'multipass') {
              // 更新多程航线的日期
              updateDate(selectedDate, currentSegment);
            }
            setDateShow('');
          },
        }}
      />
    </>
  );
};
export default memo(SearchAirline);
