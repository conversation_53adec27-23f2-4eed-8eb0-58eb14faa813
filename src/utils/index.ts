import Taro from '@tarojs/taro';

/**
 * @function toUrl
 * @description 页面跳转
 * */
export const toUrl = url => {
  Taro.navigateTo({ url });
};

/**
 * @function goBack
 * @description 页面返回
 * */
export const goBack = num => {
  const pageLen = Taro.getCurrentPages().length;
  if (pageLen > num) {
    Taro.navigateBack({ delta: num });
  } else {
    toUrl('/pages/index/index');
  }
};
/**
 * @function loading
 * @description 显示/隐藏loading
 * */
export const loading = {
  show: (title = '加载中...', mask = false) => {
    Taro.showLoading({ title, mask });
  },
  hide: () => {
    Taro.hideLoading();
  },
};

/**
 * @function checkPhone
 * @description 正则验证手机号
 * */
export const checkPhone = (phone: string) => /^1(3|4|5|6|7|8|9)\d{9}$/.test(phone);

/**
 * @function toast
 * */
export const toast = {
  show: ({ title = '', icon = 'none', duration = 3000, mask = true }) => {
    Taro.showToast({
      title,
      // @ts-ignore
      icon,
      duration,
      mask,
    });
  },
  info: (title, duration = 3000) => {
    Taro.showToast({
      title,
      icon: 'none',
      duration,
    });
  },
  hide: () => {
    Taro.hideToast();
  },
};
/**
 * @function _localStorage
 * @description localStorage封装 本地存储
 * */
export const _localStorage = {
  getItem: key => {
    try {
      var value = Taro.getStorageSync(key);
      if (value) {
        return value;
      }
    } catch (err) {
      return err;
    }
  },
  setItem: (key, data) => {
    Taro.setStorageSync(key, data);
  },
  removeItem: key => {
    try {
      Taro.removeStorageSync(key);
    } catch (e) {
      // Do something when catch error
    }
  },
  clear: () => {
    try {
      Taro.clearStorageSync();
    } catch (e) {
      // Do something when catch error
    }
  },
};
/**
 *
 * @function getWeekDay
 * @description 获取星期几
 * @param {string} dateStr 日期字符串，格式为'YYYY-MM-DD'
 *
 * */
export const getWeekDay = (dateStr: string) => {
  const [month, day] = dateStr.split('-').map(Number);
  const date = new Date(new Date().getFullYear(), month - 1, day);
  const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  return weekDays[date.getDay()];
};

/**
 * @function refreshToken
 * @description 刷新token（简化版，主要用于手动调用）
 */
export const refreshToken = async () => {
  try {
    const refreshTokenValue = _localStorage.getItem('refreshToken');
    const uid = _localStorage.getItem('uid');

    if (!refreshTokenValue || !uid) {
      throw new Error('缺少刷新token或uid');
    }

    // 动态导入apis避免循环依赖
    const { default: apis } = await import('@/api');

    const response = await apis.v10RefreshCreate({
      refreshToken: refreshTokenValue,
      uid: uid
    });

    if (response?.success && response?.data) {
      const { accessToken, refreshToken: newRefreshToken, accessTokenTimeOut, refreshTokenTimeOut } = response.data;

      // 更新存储的token信息
      _localStorage.setItem('token', accessToken);
      _localStorage.setItem('refreshToken', newRefreshToken);
      _localStorage.setItem('accessTokenTimeOut', accessTokenTimeOut);
      _localStorage.setItem('refreshTokenTimeOut', refreshTokenTimeOut);

      return true;
    } else {
      throw new Error(response?.message || '刷新token失败');
    }
  } catch (error) {
    console.error('刷新token失败:', error);
    // 清空所有token信息，跳转到登录页
    _localStorage.removeItem('token');
    _localStorage.removeItem('refreshToken');
    _localStorage.removeItem('uid');
    _localStorage.removeItem('accessTokenTimeOut');
    _localStorage.removeItem('refreshTokenTimeOut');

    // 跳转到登录页
    Taro.redirectTo({ url: '/pages/login/index' });
    return false;
  }
};

/**
 * @function checkTokenStatus
 * @description 检查token状态并返回相关信息
 */
export const checkTokenStatus = () => {
  const token = _localStorage.getItem('token');
  const accessTokenTimeOut = _localStorage.getItem('accessTokenTimeOut');
  const refreshTokenTimeOut = _localStorage.getItem('refreshTokenTimeOut');

  if (!token) {
    return { hasToken: false, isExpired: true, needRefresh: false };
  }

  const currentTime = new Date().getTime();
  const accessExpireTime = accessTokenTimeOut ? new Date(accessTokenTimeOut).getTime() : 0;
  const refreshExpireTime = refreshTokenTimeOut ? new Date(refreshTokenTimeOut).getTime() : 0;

  const isAccessTokenExpired = currentTime >= (accessExpireTime - 5 * 60 * 1000); // 提前5分钟
  const isRefreshTokenExpired = currentTime >= refreshExpireTime;

  return {
    hasToken: true,
    isExpired: isAccessTokenExpired,
    needRefresh: isAccessTokenExpired && !isRefreshTokenExpired,
    refreshExpired: isRefreshTokenExpired
  };
};
