import { memo } from 'react';
import { Image, ScrollView, Text, View } from '@tarojs/components';
import CustomHeader from '@/components/CustomHeader';
import '../lowAltitudeAirline/index.less';
import './index.less';
import { flight, hots } from '@/utils/img';
import SearchAirline from '@/components/SearchAirline';
import CustomTab from '@/components/CustomTab';
import { toUrl } from '@/utils';
import BatterySafetyNotice from '@/components/BatterySafetyNotice';
import BuyTicketNotice from '@/components/BuyTicketNotice';
import RecommendList from '@/components/RecommendList';

const JauntAirLine = () => {
  return (
    <>
      <View className='low-airline jaunt-airline'>
        <ScrollView className='scrollview' scrollY scrollWithAnimation enableBackToTop={true}>
          <CustomHeader showBackButton={true} bgColor={'#3175f9'} title={'航班查询'} />
          <CustomTab
            className={'tabs'}
            list={[
              {
                label: '单程',
                value: 'one-way',
                children: (
                  <SearchAirline
                    type={'oneWay'}
                    getData={() => {
                      toUrl('/pages/jauntAirLine/flightList/index');
                    }}
                  />
                ),
              },
              {
                label: '往返',
                value: 'round-trip',
                children: (
                  <SearchAirline
                    type={'roundTrip'}
                    getData={() => {
                      toUrl('/pages/jauntAirLine/flightList/index');
                    }}
                  />
                ),
              },
              {
                label: '多程',
                value: 'multipass',
                children: (
                  <SearchAirline
                    type={'multipass'}
                    getData={() => {
                      toUrl('/pages/jauntAirLine/flightList/index');
                    }}
                  />
                ),
              },
            ]}
            defaultValue={'one-way'}
          />
          <View className={'tips-box'}>
            <BatterySafetyNotice />
            <BuyTicketNotice />
          </View>
          <View className={'hot-recommend'}>
            <View className={'title'}>
              <Image src={hots} />
              <Text>热门短途旅行</Text>
            </View>
            <View className={'flight-bg'}>
              <Image src={flight} />
            </View>
            <RecommendList />
          </View>
        </ScrollView>
      </View>
    </>
  );
};
export default memo(JauntAirLine);
