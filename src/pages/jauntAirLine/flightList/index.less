@color-blue: #1663f8;
@color-red: #f84f2a;

.flight-list-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  position: relative;
  padding-bottom: 50px;

  .city-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 52px;
    padding: 0 16px;
    background-color: #fff;
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 500;
    color: #1c1c1c;
    .flight-icon {
      width: 13px;
      height: 12px;
      margin-right: 4px;
    }
    .cities {
      display: flex;
      align-items: center;

      .split-text {
        margin: 0 4px;
      }
    }

    .result-count {
      text {
        color: @color-blue;
      }
    }
  }

  .flight-list-container {
    padding: 0 16px;
    .flight-card {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      height: 108px;
      box-sizing: border-box;
    }
    .price-section {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: space-between;
      background: linear-gradient(
        180deg,
        rgba(72, 129, 226, 0.1) 0%,
        rgba(255, 255, 255, 0.1) 70%,
        #fff 100%
      );
      border: 1px solid;
      border-image-source: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);

      .price {
        font-size: 12px;
        font-weight: 500;
        color: @color-red;

        text {
          font-size: 20px;
        }
      }

      .select-btn {
        width: 66px;
        height: 32px;
        line-height: 32px;
        box-sizing: border-box;
        text-align: center;
        background: #fff;
        border: 1px solid #e7e7e7;
        font-size: 14px;
        color: #1c1c1c;
        font-weight: 500;
        border-radius: 99px;
      }
    }
  }

  .sort-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50px;
    background-color: #fff;
    display: flex;
    align-items: center;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);

    .sort-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;

      &.active {
        .sort-text,
        .sort-subtext {
          color: @color-blue;
        }
      }

      .sort-text {
        font-size: 14px;
        color: #333;
        margin-bottom: 2px;
      }

      .sort-subtext {
        font-size: 10px;
        color: #999;
      }
    }

    .divider {
      width: 1px;
      height: 20px;
      background-color: #eee;
    }
  }
}
