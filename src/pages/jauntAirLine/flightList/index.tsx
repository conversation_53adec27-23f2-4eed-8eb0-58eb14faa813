import { memo, useState } from 'react';
import { View, Text, ScrollView, Image, Button } from '@tarojs/components';
import CustomHeader from '@/components/CustomHeader';
import { flightIcon } from '@/utils/img';
import { toUrl } from '@/utils';
import './index.less';
import { ArrowDown } from '@nutui/icons-react-taro';
import PriceCalendar from '../components/PriceCalendar';
import FlightItem from '../components/FlightItem';

interface FlightInfo {
  id: string;
  flightNo: string;
  departureCity: string;
  arrivalCity: string;
  departureAirport: string;
  arrivalAirport: string;
  departureTerminal: string;
  arrivalTerminal: string;
  departureTime: string;
  arrivalTime: string;
  duration: string;
  price: number;
  aircraft: string;
  airline: string;
  isDirect: boolean;
  stopInfo?: string;
}

const FlightList = () => {
  const [selectedDate, setSelectedDate] = useState('');
  // const [selectedDay, setSelectedDay] = useState('');
  const [sortType, setSortType] = useState<'time' | 'price'>('time');

  // 处理日期选择
  const handleDateSelect = (date: string, _day: string) => {
    setSelectedDate(date);
    // setSelectedDay(day);
  };

  const [flights] = useState<FlightInfo[]>([
    {
      id: '1',
      flightNo: '3U6711',
      departureCity: '成都',
      arrivalCity: '绵阳',
      departureAirport: '双流国际机场',
      arrivalAirport: '南郊机场',
      departureTerminal: 'T1',
      arrivalTerminal: 'T2',
      departureTime: '09:00',
      arrivalTime: '10:50',
      duration: '1h50m',
      price: 1070,
      aircraft: '空客H135',
      airline: '川航',
      isDirect: true,
    },
    {
      id: '2',
      flightNo: '3U8512',
      departureCity: '成都',
      arrivalCity: '绵阳',
      departureAirport: '双流国际机场',
      arrivalAirport: '南郊机场',
      departureTerminal: 'T2',
      arrivalTerminal: 'T1',
      departureTime: '13:30',
      arrivalTime: '15:20',
      duration: '1h50m',
      price: 980,
      aircraft: '空客H135',
      airline: '川航',
      isDirect: false,
      stopInfo: '重庆',
    },
    {
      id: '3',
      flightNo: '3U6099',
      departureCity: '成都',
      arrivalCity: '绵阳',
      departureAirport: '双流国际机场',
      arrivalAirport: '南郊机场',
      departureTerminal: 'T1',
      arrivalTerminal: 'T1',
      departureTime: '16:00',
      arrivalTime: '17:50',
      duration: '1h50m',
      price: 1150,
      aircraft: '空客H135',
      airline: '川航',
      isDirect: true,
    },
  ]);

  // 根据排序类型对航班进行排序
  const sortedFlights = [...flights].sort((a, b) => {
    if (sortType === 'time') {
      return a.departureTime.localeCompare(b.departureTime);
    } else {
      return a.price - b.price;
    }
  });

  const handleSelectFlight = (flightId: string) => {
    // 跳转到选择舱位页面
    toUrl(`/pages/jauntAirLine/selectCabin/index?flightId=${flightId}`);
  };

  return (
    <View className='flight-list-page'>
      {/* 1. 顶部导航栏 */}
      <CustomHeader showBackButton={true} bgColor={'#3175f9'} title={'航班列表'} />

      {/* 2. 价格日历 */}
      <PriceCalendar onDateSelect={handleDateSelect} selectedDate={selectedDate} />

      {/* 3. 城市信息和搜索结果 */}
      <View className='city-info'>
        <View className='cities'>
          <Image src={flightIcon} className={'flight-icon'} />
          {flights[0].departureCity}
          <Text className={'split-text'}>到</Text>
          {flights[0].arrivalCity}
        </View>
        <Text className='result-count'>
          (共<Text>{flights.length}</Text>个航班)
        </Text>
      </View>

      {/* 4. 航班列表 */}
      <ScrollView className='scrollview' scrollY scrollWithAnimation enableBackToTop={true}>
        <View className='flight-list-container'>
          {sortedFlights.map(flight => (
            <View key={flight.id} className='flight-card'>
              <FlightItem flight={flight} />
              <View
                className='card-box price-section'
                onClick={() => handleSelectFlight(flight.id)}
              >
                <Text className='price'>
                  ¥<Text>{flight.price}</Text>起
                </Text>
                <Button className='select-btn'>选择</Button>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* 5. 底部排序功能 */}
      <View className='sort-bar'>
        <View
          className={`sort-item ${sortType === 'time' ? 'active' : ''}`}
          onClick={() => setSortType('time')}
        >
          <Text className='sort-text'>时间</Text>
          <Text className='sort-subtext'>由早至晚</Text>
        </View>
        <View className='divider'></View>
        <View
          className={`sort-item ${sortType === 'price' ? 'active' : ''}`}
          onClick={() => setSortType('price')}
        >
          <Text className='sort-text'>价格</Text>
          <ArrowDown width={12} height={12} color={sortType === 'price' ? '#3175f9' : '#666'} />
        </View>
      </View>
    </View>
  );
};

export default memo(FlightList);
