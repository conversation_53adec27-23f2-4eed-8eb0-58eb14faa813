import { memo } from 'react';
import { Image, View, Button } from '@tarojs/components';
import CustomHeader from '@/components/CustomHeader';
import './index.less';
import { toast, toUrl } from '@/utils';
import { chatIcon, infoIcon, invoiceIcon, logo, myOrderIcon } from '@/utils/img';
import useAuth from '@/hooks/useAuth';
import apis from '@/api';

const USER_MENU = [
  { name: '我的订单', icon: myOrderIcon, path: '' },
  { name: '常用信息', icon: infoIcon, path: '' },
  { name: '发票管理', icon: invoiceIcon, path: '' },
  { name: '客服中心', icon: chatIcon, path: '' },
];
const UserCenter = () => {
  const { isLoggedIn, userInfo, logout, requireAuth } = useAuth();

  const handleClick = item => {
    if (!requireAuth()) {
      return;
    }
    toUrl(item?.path);
  };

  // 测试需要认证的API调用
  const testAuthenticatedAPI = async () => {
    if (!requireAuth()) {
      return;
    }

    try {
      // 这里调用一个需要认证的API，会自动处理token刷新
      const response = await apis.v10PassengerListCreate();
      console.log('API调用成功:', response);
      toast.info('API调用成功');
    } catch (error) {
      console.error('API调用失败:', error);
      toast.info('API调用失败');
    }
  };
  return (
    <>
      <View className='user-center'>
        <CustomHeader
          showBackButton={false}
          bgColor={'transparent'}
          title={'我的'}
          titleColor={'#1D1F20'}
        />
        <View className='user-center-content'>
          <View className={'head-avatar-container'}>
            <View className={'head-avatar'}>
              <Image src={logo} />
            </View>
            <View className={'name-box'}>
              {isLoggedIn ? (
                <View className={'name'} onClick={() => toUrl('/pages/userCenter/userInfo/index')}>
                  用户ID: {userInfo?.uid || '用户名'}
                </View>
              ) : (
                <View className={'name'} onClick={() => toUrl('/pages/login/index')}>
                  登录/注册
                </View>
              )}
              <View className={'welcome-text'}>欢迎来到低空行平台</View>
            </View>
          </View>
          <View className={'card-box'}>
            {USER_MENU.map(item => {
              return (
                <View className={'card-item'} key={item?.name} onClick={() => handleClick(item)}>
                  <Image src={item?.icon} />
                  <View className={'card-item-text'}>{item?.name}</View>
                </View>
              );
            })}
          </View>

          {/* 测试区域 */}
          {isLoggedIn && (
            <View className={'test-section'}>
              <View className={'test-title'}>认证测试区域</View>
              <View className={'test-info'}>
                <View>Access Token 过期时间: {userInfo?.accessTokenTimeOut}</View>
                <View>Refresh Token 过期时间: {userInfo?.refreshTokenTimeOut}</View>
              </View>
              <View className={'test-buttons'}>
                <Button className={'test-btn'} onClick={testAuthenticatedAPI}>
                  测试需要认证的API
                </Button>
                <Button className={'logout-btn'} onClick={logout}>
                  退出登录
                </Button>
              </View>
            </View>
          )}
        </View>
      </View>
    </>
  );
};
export default memo(UserCenter);
