@import "../../styles/variables.less";
.user-center {
  height: 100vh;
  background: linear-gradient(
    180deg,
    rgba(22, 99, 248, 0.4) 0%,
    #f0f4fa 26.42%
  );
  .user-center-content {
    padding: 20px 16px;
    box-sizing: border-box;
    .head-avatar-container {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 30px;
      gap: 20px;
      image {
        width: 60px;
        height: 60px;
        border-radius: 50%;
      }
      .name-box {
        .name {
          background: linear-gradient(
            270deg,
            #2c43a0 0%,
            #0a0649 15.36%,
            #2360f8 96%
          );
          background-clip: text;
          -webkit-background-clip: text;
          color: transparent;
          font-size: 24px;
          line-height: 28px;
          margin-bottom: 8px;
        }
        .welcome-text {
          background-color: @color-primary;
          border-radius: 8px;
          padding: 4px 8px;
          font-size: 12px;
          color: #fff;
        }
      }
    }
    .card-box {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .card-item {
        flex: 1;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        color: @color-text-primary;
        image {
          width: 56px;
          height: 56px;
        }
      }
    }

    // 测试区域样式
    .test-section {
      margin-top: 30px;
      padding: 20px;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .test-title {
        font-size: 16px;
        font-weight: bold;
        color: @color-text-primary;
        margin-bottom: 15px;
        text-align: center;
      }

      .test-info {
        margin-bottom: 20px;

        view {
          font-size: 12px;
          color: #666;
          margin-bottom: 5px;
          word-break: break-all;
        }
      }

      .test-buttons {
        display: flex;
        gap: 10px;

        .test-btn {
          flex: 1;
          background-color: @color-primary;
          color: #fff;
          border-radius: 6px;
          font-size: 14px;
          height: 40px;
          line-height: 40px;
        }

        .logout-btn {
          flex: 1;
          background-color: #ff4757;
          color: #fff;
          border-radius: 6px;
          font-size: 14px;
          height: 40px;
          line-height: 40px;
        }
      }
    }
  }
}
