import { memo, useState } from "react";
import CustomHeader from "../../components/CustomHeader";
import { Button, View, Image, Input, Text } from "@tarojs/components";
import "./index.less";
import LoginProtocol from "@/components/LoginProtocol";
import { toast, toUrl } from "@/utils";
import { loginBg, wx } from "@/utils/img";

const VerCodeLoginPage = () => {
  const [isAgree, setIsAgree] = useState(false);
  const [formData, setFormData] = useState({ phone: "", code: "" });
  // 登录
  const onLogin = () => {
    if (!formData.phone) {
      toast.info("请输入手机号");
      return;
    }
    if (!formData.code) {
      toast.info("请输入验证码");
      return;
    }
    if (!isAgree) {
      toast.info("请阅读并同意遵守低空行平台《服务协议》《隐私政策》");
      return;
    }
  };
  //获取验证码
  const getCode = () => {
    if (!formData.phone) {
      toast.info("请输入手机号");
      return;
    }
    // 发送验证码
    console.log("获取验证码");
  };
  return (
    <View className="ver-code-login-page">
      <CustomHeader showBackButton={true} bgColor={"transparent"} />
      <View className={"login-bg"}>
        <Image src={loginBg} />
      </View>
      <View className={"login-container"}>
        <View className={"welcome-text"}>
          您好,<View>欢迎使用 低空行平台</View>
        </View>
        <View className={"login-content"}>
          <View className={"ver-code-header"}>验证码登录</View>
          <View className={"input-container"}>
            <View className={"input-item"}>
              <Input
                type="number"
                placeholder="请输入手机号"
                className={"inp"}
                value={formData.phone}
                onInput={(e) => {
                  setFormData({ ...formData, phone: e.detail.value });
                }}
              />
            </View>
            <View className={"input-item"}>
              <Input
                type="number"
                placeholder="请输入验证码"
                className={"inp code-inp"}
                value={formData.code}
                onInput={(e) => {
                  setFormData({ ...formData, code: e.detail.value });
                }}
              />
              <Text className={"get-code-btn"} onClick={getCode}>
                获取验证码
              </Text>
            </View>
          </View>
          <View className={"auth-msg"}>未注册手机号验证后自动创建账号</View>
          <Button className={"code-login-btn"} onClick={onLogin}>
            登录
          </Button>
          <LoginProtocol
            getData={(checked) => {
              setIsAgree(checked);
            }}
          />
          <View className={"login-footer"}>
            <Image
              src={wx}
              className={"footer-logo"}
              onClick={() => toUrl("/pages/login/index")}
            />
          </View>
        </View>
      </View>
    </View>
  );
};
export default memo(VerCodeLoginPage);
