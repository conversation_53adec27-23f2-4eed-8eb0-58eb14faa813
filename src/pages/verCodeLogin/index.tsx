import { memo, useState, useEffect, useRef } from 'react';
import CustomHeader from '../../components/CustomHeader';
import { Button, View, Image, Input, Text } from '@tarojs/components';
import './index.less';
import LoginProtocol from '@/components/LoginProtocol';
import { toast, toUrl, checkPhone } from '@/utils';
import { loginBg, wx } from '@/utils/img';
import apis from '@/api';

const VerCodeLoginPage = () => {
  const [isAgree, setIsAgree] = useState(false);
  const [formData, setFormData] = useState({ phone: '', code: '', captchaCode: '' });
  const [captchaData, setCaptchaData] = useState({ captchaBase64: '', captchaKey: '' });
  const [countdown, setCountdown] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const countdownTimer = useRef<NodeJS.Timeout | null>(null);

  // 获取图形验证码
  const getCaptcha = async () => {
    try {
      setIsLoading(true);
      const response = await apis.v10CaptchaImageCreate();
      if (response?.success && response?.data) {
        setCaptchaData({
          captchaBase64: response.data.captchaBase64 || '',
          captchaKey: response.data.captchaKey || '',
        });
      } else {
        toast.info('获取图形验证码失败，请重试');
      }
    } catch (error) {
      toast.info('获取图形验证码失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 开始倒计时
  const startCountdown = () => {
    setCountdown(60);
    countdownTimer.current = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          if (countdownTimer.current) {
            clearInterval(countdownTimer.current);
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // 登录
  const onLogin = () => {
    if (!formData.phone) {
      toast.info('请输入手机号');
      return;
    }
    if (!checkPhone(formData.phone)) {
      toast.info('请输入正确的手机号');
      return;
    }
    if (!formData.code) {
      toast.info('请输入验证码');
      return;
    }
    if (!isAgree) {
      toast.info('请阅读并同意遵守低空行平台《服务协议》《隐私政策》');
      return;
    }
    // TODO: 调用登录接口
    console.log('登录', formData);
  };

  // 获取短信验证码
  const getCode = async () => {
    if (!formData.phone) {
      toast.info('请输入手机号');
      return;
    }
    if (!checkPhone(formData.phone)) {
      toast.info('请输入正确的手机号');
      return;
    }
    if (!formData.captchaCode) {
      toast.info('请输入图形验证码');
      return;
    }
    if (!captchaData.captchaKey) {
      toast.info('请先获取图形验证码');
      return;
    }
    if (countdown > 0) {
      return;
    }

    try {
      setIsLoading(true);
      const response = await apis.v10SendSmsCodeCreate({
        phoneNumber: formData.phone,
        captchaCode: formData.captchaCode,
        captchaKey: captchaData.captchaKey,
      });

      if (response?.success) {
        toast.info('验证码发送成功');
        startCountdown();
        // 清空图形验证码输入
        setFormData(prev => ({ ...prev, captchaCode: '' }));
        // 重新获取图形验证码
        getCaptcha();
      } else {
        toast.info(response?.message || '发送验证码失败，请重试');
        // 重新获取图形验证码
        getCaptcha();
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      toast.info('发送验证码失败，请重试');
      // 重新获取图形验证码
      getCaptcha();
    } finally {
      setIsLoading(false);
    }
  };

  // 组件挂载时获取图形验证码
  useEffect(() => {
    getCaptcha();

    // 清理定时器
    return () => {
      if (countdownTimer.current) {
        clearInterval(countdownTimer.current);
      }
    };
  }, []);
  return (
    <View className='ver-code-login-page'>
      <CustomHeader showBackButton={true} bgColor={'transparent'} />
      <View className={'login-bg'}>
        <Image src={loginBg} />
      </View>
      <View className={'login-container'}>
        <View className={'welcome-text'}>
          您好,<View>欢迎使用 低空行平台</View>
        </View>
        <View className={'login-content'}>
          <View className={'ver-code-header'}>验证码登录</View>
          <View className={'input-container'}>
            <View className={'input-item'}>
              <Input
                type='number'
                placeholder='请输入手机号'
                className={'inp'}
                value={formData.phone}
                onInput={e => {
                  setFormData({ ...formData, phone: e.detail.value });
                }}
              />
            </View>

            {/* 图形验证码输入框 */}
            <View className={'input-item captcha-item'}>
              <Input
                placeholder='请输入图形验证码'
                className={'inp captcha-inp'}
                value={formData.captchaCode}
                onInput={e => {
                  setFormData({ ...formData, captchaCode: e.detail.value });
                }}
              />
              <View className={'captcha-image-container'} onClick={getCaptcha}>
                {captchaData.captchaBase64 ? (
                  <Image src={captchaData.captchaBase64} className={'captcha-image'} />
                ) : (
                  <View className={'captcha-loading'}>{isLoading ? '加载中...' : '点击获取'}</View>
                )}
              </View>
            </View>

            <View className={'input-item'}>
              <Input
                type='number'
                placeholder='请输入短信验证码'
                className={'inp code-inp'}
                value={formData.code}
                onInput={e => {
                  setFormData({ ...formData, code: e.detail.value });
                }}
              />
              <Text
                className={`get-code-btn ${countdown > 0 || isLoading ? 'disabled' : ''}`}
                onClick={getCode}
              >
                {countdown > 0 ? `${countdown}s` : isLoading ? '发送中...' : '获取验证码'}
              </Text>
            </View>
          </View>
          <View className={'auth-msg'}>未注册手机号验证后自动创建账号</View>
          <Button className={'code-login-btn'} onClick={onLogin}>
            登录
          </Button>
          <LoginProtocol
            getData={checked => {
              setIsAgree(checked);
            }}
          />
          <View className={'login-footer'}>
            <Image src={wx} className={'footer-logo'} onClick={() => toUrl('/pages/login/index')} />
          </View>
        </View>
      </View>
    </View>
  );
};
export default memo(VerCodeLoginPage);
