@import "../../styles/variables.less";

.ver-code-login-page {
  width: 100%;
  height: 100vh;
  overflow-y: hidden;
  .login-bg {
    width: 100%;
    height: 224px;
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    image {
      width: 100%;
      height: 224px;
    }
  }
  .login-container {
    width: 100%;
    height: 100%;
    margin-top: 20px;

    .welcome-text {
      color: #fff;
      font-size: 20px;
      margin-left: 20px;
      margin-bottom: 20px;

      view {
        font-size: 14px;
        margin-top: 10px;
      }
    }
    .login-content {
      width: 100%;
      height: calc(100vh - 224px);
      background-color: #fff;
      border-radius: 20px 20px 0 0;
      box-sizing: border-box;
      padding: 20px;
      .ver-code-header {
        font-size: 20px;
        font-weight: bold;
        color: @color-text-primary;
        margin-bottom: 20px;
      }
      .input-item {
        border: 1px solid #d3d8e5;
        border-radius: 6px;
        width: 100%;
        height: 42px;
        padding: 0 10px;
        margin-bottom: 10px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .inp {
          height: 40px;
          &::placeholder {
            color: #b4bacc;
            font-weight: 400;
          }
        }
        .code-inp {
          width: 70%;
        }
        .get-code-btn {
          color: @color-primary;
          font-size: 14px;
          font-weight: 400;
          padding-left: 10px;
        }
      }
    }

    .auth-msg {
      color: @color-gray-98;
      margin-bottom: 20px;
      margin-top: 16px;
      text-align: left;
    }
    .code-login-btn {
      width: 350px;
      height: 50px;
      background-color: @color-primary;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: bold;
      border-radius: 6px;
      margin-bottom: 20px;
      margin-top: 40px;
    }
    .login-footer {
      position: absolute;
      bottom: 40px;
      left: 0;
      right: 0;
      image {
        width: 40px;
        height: 40px;
        margin: 0 auto;
      }
    }
  }
}
