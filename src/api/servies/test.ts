/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** BaseResult */
export interface BaseResult {
  /** @format int32 */
  code?: number;
  data?: object;
  message?: string;
}

/** BaseResultOfHtProduct对象 */
export interface BaseResultOfHtProduct对象 {
  /** @format int32 */
  code?: number;
  /** 产品表 */
  data?: HtProduct对象;
  message?: string;
}

/** BaseResultOfListOfHtProduct对象 */
export interface BaseResultOfListOfHtProduct对象 {
  /** @format int32 */
  code?: number;
  data?: HtProduct对象[];
  message?: string;
}

/** BaseResultOfListOfOrderListVO */
export interface BaseResultOfListOfOrderListVO {
  /** @format int32 */
  code?: number;
  data?: OrderListVO[];
  message?: string;
}

/** BaseResultOfListOfProductCodeVo */
export interface BaseResultOfListOfProductCodeVo {
  /** @format int32 */
  code?: number;
  data?: ProductCodeVo[];
  message?: string;
}

/** BaseResultOfOrderInfoVo */
export interface BaseResultOfOrderInfoVo {
  /** @format int32 */
  code?: number;
  /** 订单详情展示对象 */
  data?: OrderInfoVo;
  message?: string;
}

/** BaseResultOfProductCodeCheckVo */
export interface BaseResultOfProductCodeCheckVo {
  /** @format int32 */
  code?: number;
  /** 券码检查结果展示对象 */
  data?: ProductCodeCheckVo;
  message?: string;
}

/** BaseResultOfProductOrderVo */
export interface BaseResultOfProductOrderVo {
  /** @format int32 */
  code?: number;
  /** 产品订单展示对象 */
  data?: ProductOrderVo;
  message?: string;
}

/** BaseResultOfboolean */
export interface BaseResultOfboolean {
  /** @format int32 */
  code?: number;
  data?: boolean;
  message?: string;
}

/** BaseResultOfint */
export interface BaseResultOfint {
  /** @format int32 */
  code?: number;
  /** @format int32 */
  data?: number;
  message?: string;
}

/** BaseResultOfobject */
export interface BaseResultOfobject {
  /** @format int32 */
  code?: number;
  data?: object;
  message?: string;
}

/** BaseResultOfstring */
export interface BaseResultOfstring {
  /** @format int32 */
  code?: number;
  data?: string;
  message?: string;
}

/** DataPermissionsSaveDto */
export interface DataPermissionsSaveDto {
  /** 权限类型 */
  permissionType?: string;
  /** 允许被查看数据的角色id集合 */
  permissionsRoleIdList?: string[];
  /** 角色id */
  roleId?: string;
}

/**
 * HtProduct对象
 * 产品表
 */
export interface HtProduct对象 {
  /**
   * 删除标识，1：正常；2：删除
   * @format int32
   */
  deleted?: number;
  /** 产品描述 */
  discribe?: string;
  /** 产品使用时间 */
  effectTime?: string;
  /**
   * 产品有效期，1：长期有效；2：当日有效
   * @format int32
   */
  effectType?: number;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 产品图片 */
  image?: string;
  /** 产品价格 */
  price?: string;
  /** 产品名称 */
  productName?: string;
  /**
   * 产品类型，1：员工次卡；2：旅客高舱
   * @format int32
   */
  productType?: number;
  /**
   * 状态，1：上架；2：下架
   * @format int32
   */
  status?: number;
  /**
   * 次数
   * @format int32
   */
  times?: number;
}

/**
 * HtRefundAuditRecode
 * 退款申请审核记录表
 */
export interface HtRefundAuditRecode {
  /** 申请原因 */
  applyReason?: string;
  /**
   * 申请时间
   * @format date-time
   */
  applyTime?: string;
  /** 申请人 */
  applyUser?: string;
  /** 申请人工号 */
  applyUserNo?: string;
  /** 审核结果 */
  auditResult?: string;
  /**
   * 审批时间
   * @format date-time
   */
  auditTime?: string;
  /** 审核人 */
  auditUser?: string;
  /** 审核人工号 */
  auditUserNo?: string;
  /**
   * 创建时间
   * @format date-time
   */
  createdTime?: string;
  /** id */
  id?: string;
  /** 订单号 */
  orderNo?: string;
  /** 支付单号 */
  payOrderNo?: string;
  /** 退款单号 */
  refundOrderNo?: string;
}

/** ModelAndView */
export type ModelAndView = object;

/**
 * OrderAddVo
 * 订单新增对象
 */
export interface OrderAddVo {
  /** 旅客姓名 */
  passengerName?: string;
  /** 手机号 */
  phone?: string;
  /**
   * 产品id
   * @format int64
   */
  productId?: number;
  /** 手机号 */
  userNo?: string;
}

/**
 * OrderInfoVo
 * 订单详情展示对象
 */
export interface OrderInfoVo {
  /** 登机口 */
  boardGate?: string;
  /**
   * 取消时间
   * @format date-time
   */
  cancelTime?: string;
  /**
   * 取消类型，1：手动取消
   * @format int32
   */
  cancelType?: number;
  /** 券码编号 */
  codeNo?: string;
  /** 券码集合 */
  codeNoList?: string[];
  /** 创建人姓名 */
  createdName?: string;
  /** 创建人工号 */
  createdNo?: string;
  /**
   * 创建时间
   * @format date-time
   */
  createdTime?: string;
  /** 产品使用时间 */
  effectTime?: string;
  /** 航班日期 */
  fltDate?: string;
  /** 航班号 */
  fltNo?: string;
  /** 订单号 */
  orderNo?: string;
  /** 旅客姓名 */
  passengerName?: string;
  /** 支付订单号 */
  payOrderNo?: string;
  /**
   * 支付时间
   * @format date-time
   */
  payTime?: string;
  /** 手机号 */
  phone?: string;
  /** 价格 */
  price?: string;
  /** 产品名称 */
  productName?: string;
  /**
   * 产品类型，1：员工次卡；2：旅客高舱
   * @format int32
   */
  productType?: number;
  /** 退款订单号 */
  refundPayOrderNo?: string;
  /** 退款审批记录 */
  refundRecode?: HtRefundAuditRecode[];
  /** 退款状态 0发起退款 待审核  1退款审核通过 待退款  2退款中  3已退款  4退款失败  5关闭 */
  refundStatus?: string;
  /**
   * 退款到账时间
   * @format date-time
   */
  refundSuccessTime?: string;
  /** 座位号 */
  seatNo?: string;
  /**
   * 状态，1：待支付；2：已取消；3：待使用；4：退款中；5：已退款；6：使用中；7：已使用
   * @format int32
   */
  status?: number;
  /**
   * 次数
   * @format int32
   */
  times?: number;
  /** 产品使用次数 */
  useTimes?: string;
  /**
   * 核销时间
   * @format date-time
   */
  verifyTime?: string;
  /** 核销人姓名 */
  verifyUserName?: string;
  /** 核销人工号 */
  verifyUserNo?: string;
}

/** OrderItem */
export interface OrderItem {
  asc?: boolean;
  column?: string;
}

/** OrderListDto */
export interface OrderListDto {
  /** 创建日期-结束 */
  createdDateEnd?: string;
  /** 创建日期-开始 */
  createdDateStart?: string;
  /** 创建人姓名 */
  createdName?: string;
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 排序列名(orderByColumn) */
  obc?: string;
  /** 状态，1：待支付；2：已取消；3：待使用；4：退款中；5：已退款；6：使用中；7：已使用 */
  orderStatus?: string;
  /** 订单类型，1：员工次卡；2：旅客高舱 */
  orderType?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
}

/**
 * OrderListVO
 * 订单列表展示对象
 */
export interface OrderListVO {
  /** 创建人姓名 */
  createdName?: string;
  /**
   * 创建时间
   * @format date-time
   */
  createdTime?: string;
  /** 产品价格 */
  price?: string;
  /**
   * 产品id
   * @format int64
   */
  productId?: number;
  /** 产品名称 */
  productName?: string;
  /**
   * 产品类型，1：员工次卡；2：旅客高舱
   * @format int32
   */
  productType?: number;
  /**
   * 状态，1：待支付；2：已取消；3：待使用；4：退款中；5：已退款；6：使用中；7：已使用
   * @format int32
   */
  status?: number;
}

/**
 * OrderPassengerAddVo
 * 订单旅客对象
 */
export interface OrderPassengerAddVo {
  /** 登机口 */
  boardGate?: string;
  /** 航班日期 */
  fltDate?: string;
  /** 航班号 */
  fltNo?: string;
  /** 旅客姓名 */
  passengerName?: string;
  /** 手机号 */
  phone?: string;
  /**
   * 产品id
   * @format int64
   */
  productId?: number;
  /** 座位号 */
  seatNo?: string;
}

/**
 * OrderQueryVo
 * 订单列表查询对象
 */
export interface OrderQueryVo {
  /** 创建人 */
  createdBy?: string;
  /** 创建结束日期 */
  createdEndTime?: string;
  /** 创建开始日期 */
  createdStartTime?: string;
  /**
   * 订单类型，1：员工次卡；2：旅客高舱
   * @format int32
   */
  orderType?: number;
  /** 状态，1：待支付；2：已取消；3：待使用；4：退款中；5：已退款；6：使用中；7：已使用 */
  statusList?: number[];
}

/** PagedResultOfListOfHtProduct对象 */
export interface PagedResultOfListOfHtProduct对象 {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: HtProduct对象[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/**
 * ProductCodeCheckVo
 * 券码检查结果展示对象
 */
export interface ProductCodeCheckVo {
  /** 券码编号 */
  codeNo?: string;
  /** 订单号 */
  orderNo?: string;
  /** 产品名称 */
  productName?: string;
}

/**
 * ProductCodeVo
 * 我的券码展示对象
 */
export interface ProductCodeVo {
  /** 券码编号 */
  codeNo?: string;
  /** 产品描述 */
  discribe?: string;
  /** 产品名称 */
  productName?: string;
  /**
   * 产品类型，1：员工次卡；2：旅客高舱
   * @format int32
   */
  productType?: number;
  /**
   * 状态，1：正常；2：已使用；3：已失效
   * @format int32
   */
  status?: number;
}

/**
 * ProductOrderVo
 * 产品订单展示对象
 */
export interface ProductOrderVo {
  /**
   * 创建日期
   * @format date-time
   */
  createdTime?: string;
  /** 产品名称 */
  productName?: string;
  /**
   * 产品的次数
   * @format int32
   */
  times?: number;
  /**
   * 使用次数
   * @format int32
   */
  useTimes?: number;
}

/**
 * ProductQueryVo
 * 产品查询条件
 */
export interface ProductQueryVo {
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /** 产品名称 */
  productName?: string;
  /**
   * 产品类型，1：员工次卡；2：旅客高舱
   * @format int32
   */
  productType?: number;
}

/**
 * ProductUpdateStatusVo
 * 产品状态更新对象
 */
export interface ProductUpdateStatusVo {
  /**
   * 主键
   * @format int64
   */
  id?: number;
  /**
   * 状态，1：上架；2：下架
   * @format int32
   */
  status?: number;
}

/**
 * ProductUserCodeVerifyVo
 * 用户券码核销对象
 */
export interface ProductUserCodeVerifyVo {
  /** 用户券码对象 */
  productUserCodeVoList?: ProductUserCodeVo[];
}

/**
 * ProductUserCodeVo
 * 用户券码对象
 */
export interface ProductUserCodeVo {
  /** 登机口 */
  boardGate?: string;
  /** 券码编号 */
  codeNo?: string;
  /** 航班日期 */
  fltDate?: string;
  /** 航班号 */
  fltNo?: string;
  /** 订单号 */
  orderNo?: string;
  /** 旅客姓名 */
  passengerName?: string;
  /** 手机号 */
  phone?: string;
  /** 座位号 */
  seatNo?: string;
}

import request from '../apiConfig';

export enum ContentType {
  Json = 'application/json',
  FormData = 'multipart/form-data',
  UrlEncoded = 'application/x-www-form-urlencoded',
  Text = 'text/plain',
}
export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams extends Omit<any, 'data' | 'params' | 'url' | 'responseType'> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<FullRequestParams, 'body' | 'method' | 'query' | 'path'>;

/**
 * @title high-tank-services Api Doc
 * @version Application Version：3.0.6-RC1-@timestamp@
 * @baseUrl http://**************:8002
 * @contact swcares team <<EMAIL>>
 */
export class Api {
  api = {
    /**
     * No description
     *
     * @tags 小程序-订单接口
     * @name AddUsingPost1
     * @summary 新增次卡订单
     * @request POST:/api/high-tank/app/order/add
     * @secure
     */
    addUsingPost1: (data: OrderAddVo, params: RequestParams = {}) =>
      request<BaseResultOfstring, void>({
        path: `/api/high-tank/app/order/add`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 小程序-订单接口
     * @name AddUsingPost
     * @summary 新增高舱订单
     * @request POST:/api/high-tank/app/order/add/passenger
     * @secure
     */
    addUsingPost: (data: OrderPassengerAddVo, params: RequestParams = {}) =>
      request<BaseResultOfstring, void>({
        path: `/api/high-tank/app/order/add/passenger`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 小程序-订单接口
     * @name CancalUsingGet
     * @summary 订单取消
     * @request GET:/api/high-tank/app/order/cancal
     * @secure
     */
    cancalUsingGet: (
      query: {
        /** 订单号 */
        orderNo: string;
      },
      params: RequestParams = {}
    ) =>
      request<BaseResultOfboolean, void>({
        path: `/api/high-tank/app/order/cancal`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 小程序-订单接口
     * @name InfoUsingGet
     * @summary 详情
     * @request GET:/api/high-tank/app/order/info
     * @secure
     */
    infoUsingGet: (
      query: {
        /** 订单号 */
        orderNo: string;
      },
      params: RequestParams = {}
    ) =>
      request<BaseResultOfOrderInfoVo, void>({
        path: `/api/high-tank/app/order/info`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 小程序-订单接口
     * @name ListUsingPost
     * @summary 订单列表
     * @request POST:/api/high-tank/app/order/list
     * @secure
     */
    listUsingPost: (data: OrderQueryVo, params: RequestParams = {}) =>
      request<BaseResultOfListOfOrderListVO, void>({
        path: `/api/high-tank/app/order/list`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 小程序-订单接口
     * @name QueryStatusUsingGet
     * @summary 订单状态
     * @request GET:/api/high-tank/app/order/status
     * @secure
     */
    queryStatusUsingGet: (
      query: {
        /** 订单号 */
        orderNo: string;
      },
      params: RequestParams = {}
    ) =>
      request<BaseResultOfint, void>({
        path: `/api/high-tank/app/order/status`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 小程序-券码接口
     * @name CodeCheckUsingGet
     * @summary 券码检查
     * @request GET:/api/high-tank/app/productCode/codeNoCheck
     * @secure
     */
    codeCheckUsingGet: (
      query: {
        /** 券码 */
        codeNo: string;
      },
      params: RequestParams = {}
    ) =>
      request<BaseResultOfProductCodeCheckVo, void>({
        path: `/api/high-tank/app/productCode/codeNoCheck`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 小程序-券码接口
     * @name MyListUsingGet
     * @summary 我的券码列表
     * @request GET:/api/high-tank/app/productCode/list/my
     * @secure
     */
    myListUsingGet: (
      query: {
        /**
         * 状态，1：正常；2：已使用；3：已失效
         * @format int32
         */
        status: number;
      },
      params: RequestParams = {}
    ) =>
      request<BaseResultOfListOfProductCodeVo, void>({
        path: `/api/high-tank/app/productCode/list/my`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 小程序-券码接口
     * @name ProductOrderInfoUsingGet
     * @summary 核销时产品信息
     * @request GET:/api/high-tank/app/productCode/productOrderInfo
     * @secure
     */
    productOrderInfoUsingGet: (
      query: {
        /** 工号 */
        userNo: string;
      },
      params: RequestParams = {}
    ) =>
      request<BaseResultOfProductOrderVo, void>({
        path: `/api/high-tank/app/productCode/productOrderInfo`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 小程序-券码接口
     * @name VerifyUsingPost
     * @summary 券码核销
     * @request POST:/api/high-tank/app/productCode/verify
     * @secure
     */
    verifyUsingPost: (data: ProductUserCodeVerifyVo, params: RequestParams = {}) =>
      request<BaseResultOfboolean, void>({
        path: `/api/high-tank/app/productCode/verify`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 小程序-产品接口
     * @name InfoUsingGet1
     * @summary 详情
     * @request GET:/api/high-tank/app/product/info
     * @secure
     */
    infoUsingGet1: (
      query: {
        /**
         * id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {}
    ) =>
      request<BaseResultOfHtProduct对象, void>({
        path: `/api/high-tank/app/product/info`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 小程序-产品接口
     * @name ListUsingGet
     * @summary 产品列表
     * @request GET:/api/high-tank/app/product/list
     * @secure
     */
    listUsingGet: (
      query: {
        /**
         * 产品类型，1：员工次卡；2：旅客高舱
         * @format int32
         */
        productType: number;
      },
      params: RequestParams = {}
    ) =>
      request<BaseResultOfListOfHtProduct对象, void>({
        path: `/api/high-tank/app/product/list`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 产品表接口
     * @name SaveUsingPost
     * @summary 保存权限配置
     * @request POST:/api/high-tank/data_permissons/save
     * @secure
     */
    saveUsingPost: (data: DataPermissionsSaveDto, params: RequestParams = {}) =>
      request<BaseResult, void>({
        path: `/api/high-tank/data_permissons/save`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags filter-error-controller
     * @name ErrorUsingGet
     * @summary error
     * @request GET:/api/high-tank/error/exthrow
     * @secure
     */
    errorUsingGet: (params: RequestParams = {}) =>
      request<void, void>({
        path: `/api/high-tank/error/exthrow`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags filter-error-controller
     * @name ErrorUsingPut
     * @summary error
     * @request PUT:/api/high-tank/error/exthrow
     * @secure
     */
    errorUsingPut: (params: RequestParams = {}) =>
      request<void, void>({
        path: `/api/high-tank/error/exthrow`,
        method: 'PUT',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags filter-error-controller
     * @name ErrorUsingPost
     * @summary error
     * @request POST:/api/high-tank/error/exthrow
     * @secure
     */
    errorUsingPost: (params: RequestParams = {}) =>
      request<void, void>({
        path: `/api/high-tank/error/exthrow`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags filter-error-controller
     * @name ErrorUsingDelete
     * @summary error
     * @request DELETE:/api/high-tank/error/exthrow
     * @secure
     */
    errorUsingDelete: (params: RequestParams = {}) =>
      request<void, void>({
        path: `/api/high-tank/error/exthrow`,
        method: 'DELETE',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags filter-error-controller
     * @name ErrorUsingOptions
     * @summary error
     * @request OPTIONS:/api/high-tank/error/exthrow
     * @secure
     */
    errorUsingOptions: (params: RequestParams = {}) =>
      request<void, void>({
        path: `/api/high-tank/error/exthrow`,
        method: 'OPTIONS',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags filter-error-controller
     * @name ErrorUsingHead
     * @summary error
     * @request HEAD:/api/high-tank/error/exthrow
     * @secure
     */
    errorUsingHead: (params: RequestParams = {}) =>
      request<void, void>({
        path: `/api/high-tank/error/exthrow`,
        method: 'HEAD',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags filter-error-controller
     * @name ErrorUsingPatch
     * @summary error
     * @request PATCH:/api/high-tank/error/exthrow
     * @secure
     */
    errorUsingPatch: (params: RequestParams = {}) =>
      request<void, void>({
        path: `/api/high-tank/error/exthrow`,
        method: 'PATCH',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags filter-error-controller
     * @name ErrorUsingTrace
     * @summary error
     * @request TRACE:/api/high-tank/error/exthrow
     * @secure
     */
    errorUsingTrace: (params: RequestParams = {}) =>
      request<void, void>({
        path: `/api/high-tank/error/exthrow`,
        method: 'TRACE',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 支付回调
     * @name PayNotifyUsingPost
     * @summary payNotify
     * @request POST:/api/high-tank/notify/chinaPay
     * @secure
     */
    payNotifyUsingPost: (params: RequestParams = {}) =>
      request<string, void>({
        path: `/api/high-tank/notify/chinaPay`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 订单接口
     * @name InfoUsingGet2
     * @summary 订单详情
     * @request GET:/api/high-tank/order/info
     * @secure
     */
    infoUsingGet2: (
      query: {
        /** 订单号 */
        orderNo: string;
      },
      params: RequestParams = {}
    ) =>
      request<BaseResultOfOrderInfoVo, void>({
        path: `/api/high-tank/order/info`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 订单接口
     * @name ListUsingPost1
     * @summary 订单列表查询
     * @request POST:/api/high-tank/order/list
     * @secure
     */
    listUsingPost1: (data: OrderListDto, params: RequestParams = {}) =>
      request<BaseResultOfListOfOrderListVO, void>({
        path: `/api/high-tank/order/list`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 订单接口
     * @name RefundApplyUsingGet
     * @summary 订单退款申请
     * @request GET:/api/high-tank/order/refund_apply
     * @secure
     */
    refundApplyUsingGet: (
      query: {
        /** 订单号 */
        orderNo: string;
      },
      params: RequestParams = {}
    ) =>
      request<BaseResult, void>({
        path: `/api/high-tank/order/refund_apply`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 订单接口
     * @name UpdateOrderUsingGet
     * @summary 订单信息更新测试
     * @request GET:/api/high-tank/order/updateOrder
     * @secure
     */
    updateOrderUsingGet: (
      query: {
        /** orderNo */
        orderNo: string;
      },
      params: RequestParams = {}
    ) =>
      request<BaseResultOfboolean, void>({
        path: `/api/high-tank/order/updateOrder`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 产品接口
     * @name SaveUsingPost1
     * @summary 新建
     * @request POST:/api/high-tank/product/add
     * @secure
     */
    saveUsingPost1: (data: HtProduct对象, params: RequestParams = {}) =>
      request<BaseResultOfobject, void>({
        path: `/api/high-tank/product/add`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 产品接口
     * @name DeleteUsingPost
     * @summary 删除
     * @request POST:/api/high-tank/product/delete
     * @secure
     */
    deleteUsingPost: (data: Record<string, number[]>, params: RequestParams = {}) =>
      request<BaseResultOfobject, void>({
        path: `/api/high-tank/product/delete`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 产品接口
     * @name GetUsingGet
     * @summary 详情
     * @request GET:/api/high-tank/product/info
     * @secure
     */
    getUsingGet: (
      query: {
        /**
         * id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {}
    ) =>
      request<BaseResultOfHtProduct对象, void>({
        path: `/api/high-tank/product/info`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 产品接口
     * @name PageUsingPost
     * @summary 分页
     * @request POST:/api/high-tank/product/page
     * @secure
     */
    pageUsingPost: (data: ProductQueryVo, params: RequestParams = {}) =>
      request<PagedResultOfListOfHtProduct对象, void>({
        path: `/api/high-tank/product/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 产品接口
     * @name UpdateUsingPost
     * @summary 修改
     * @request POST:/api/high-tank/product/update
     * @secure
     */
    updateUsingPost: (data: HtProduct对象, params: RequestParams = {}) =>
      request<BaseResultOfobject, void>({
        path: `/api/high-tank/product/update`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 产品接口
     * @name UpdateStatusUsingPost
     * @summary 更新状态
     * @request POST:/api/high-tank/product/updateStatus
     * @secure
     */
    updateStatusUsingPost: (data: ProductUpdateStatusVo, params: RequestParams = {}) =>
      request<BaseResultOfobject, void>({
        path: `/api/high-tank/product/updateStatus`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * @description 上传产品图片，返回图片访问URL
     *
     * @tags 产品接口
     * @name UploadUsingPost1
     * @summary 上传图片
     * @request POST:/api/high-tank/product/upload
     * @secure
     */
    uploadUsingPost1: (data: File, params: RequestParams = {}) =>
      request<BaseResultOfstring, void>({
        path: `/api/high-tank/product/upload`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags sso-approval-endpoint
     * @name GetAccessConfirmationUsingGet
     * @summary getAccessConfirmation
     * @request GET:/api/high-tank/oauth/confirm_access
     * @secure
     */
    getAccessConfirmationUsingGet: (
      query?: {
        /** model */
        model?: object;
      },
      params: RequestParams = {}
    ) =>
      request<ModelAndView, void>({
        path: `/api/high-tank/oauth/confirm_access`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags sso-approval-endpoint
     * @name GetAccessConfirmationUsingPut
     * @summary getAccessConfirmation
     * @request PUT:/api/high-tank/oauth/confirm_access
     * @secure
     */
    getAccessConfirmationUsingPut: (
      query?: {
        /** model */
        model?: object;
      },
      params: RequestParams = {}
    ) =>
      request<ModelAndView, void>({
        path: `/api/high-tank/oauth/confirm_access`,
        method: 'PUT',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags sso-approval-endpoint
     * @name GetAccessConfirmationUsingPost
     * @summary getAccessConfirmation
     * @request POST:/api/high-tank/oauth/confirm_access
     * @secure
     */
    getAccessConfirmationUsingPost: (
      query?: {
        /** model */
        model?: object;
      },
      params: RequestParams = {}
    ) =>
      request<ModelAndView, void>({
        path: `/api/high-tank/oauth/confirm_access`,
        method: 'POST',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags sso-approval-endpoint
     * @name GetAccessConfirmationUsingDelete
     * @summary getAccessConfirmation
     * @request DELETE:/api/high-tank/oauth/confirm_access
     * @secure
     */
    getAccessConfirmationUsingDelete: (
      query?: {
        /** model */
        model?: object;
      },
      params: RequestParams = {}
    ) =>
      request<ModelAndView, void>({
        path: `/api/high-tank/oauth/confirm_access`,
        method: 'DELETE',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags sso-approval-endpoint
     * @name GetAccessConfirmationUsingOptions
     * @summary getAccessConfirmation
     * @request OPTIONS:/api/high-tank/oauth/confirm_access
     * @secure
     */
    getAccessConfirmationUsingOptions: (
      query?: {
        /** model */
        model?: object;
      },
      params: RequestParams = {}
    ) =>
      request<ModelAndView, void>({
        path: `/api/high-tank/oauth/confirm_access`,
        method: 'OPTIONS',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags sso-approval-endpoint
     * @name GetAccessConfirmationUsingHead
     * @summary getAccessConfirmation
     * @request HEAD:/api/high-tank/oauth/confirm_access
     * @secure
     */
    getAccessConfirmationUsingHead: (
      query?: {
        /** model */
        model?: object;
      },
      params: RequestParams = {}
    ) =>
      request<ModelAndView, void>({
        path: `/api/high-tank/oauth/confirm_access`,
        method: 'HEAD',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags sso-approval-endpoint
     * @name GetAccessConfirmationUsingPatch
     * @summary getAccessConfirmation
     * @request PATCH:/api/high-tank/oauth/confirm_access
     * @secure
     */
    getAccessConfirmationUsingPatch: (
      query?: {
        /** model */
        model?: object;
      },
      params: RequestParams = {}
    ) =>
      request<ModelAndView, void>({
        path: `/api/high-tank/oauth/confirm_access`,
        method: 'PATCH',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags sso-approval-endpoint
     * @name GetAccessConfirmationUsingTrace
     * @summary getAccessConfirmation
     * @request TRACE:/api/high-tank/oauth/confirm_access
     * @secure
     */
    getAccessConfirmationUsingTrace: (
      query?: {
        /** model */
        model?: object;
      },
      params: RequestParams = {}
    ) =>
      request<ModelAndView, void>({
        path: `/api/high-tank/oauth/confirm_access`,
        method: 'TRACE',
        query: query,
        secure: true,
        ...params,
      }),
  };
}
