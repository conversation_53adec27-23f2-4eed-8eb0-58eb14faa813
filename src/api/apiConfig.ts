import { TOKEN_NAME } from '@/utils/constant';
import Taro from '@tarojs/taro';

interface RequestOptions {
  path: string;
  method?: keyof Taro.request.Method;
  type?: any;
  body?: any;
  query?: any;
  format?: any;
  secure?: boolean;
}

const BASE_URL = 'http://**************:8002'; // 替换成你的API基础地址

// 假设你的token存储在本地存储中
const getToken = () => {
  return { Authorization: `Bearer ${Taro.getStorageSync(TOKEN_NAME)}` }; // 携带token}
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const request = async <T = any, _E = any>(
  options: RequestOptions & {
    isloading?: boolean;
  }
): Promise<T> => {
  // 设置请求默认值
  const { path, method = 'GET', body, query, type, isloading = false, ...params } = options;

  try {
    // 请求拦截器
    isloading && Taro.showLoading({ title: '加载中...' });

    // 发起请求
    const response = await Taro.request({
      url: BASE_URL + path, // 请求地址
      method: method, // 请求方法
      data: body || query, // 发送的数据
      header: {
        'content-type': type || 'application/json', // 默认头部
        appSign: 'HXGW',
        ...getToken(),
        ...((params as any)?.header ?? {}), // 自定义头部
      },
      ...params,
    });

    isloading && Taro.hideLoading();

    // 处理返回
    if (response.statusCode >= 200 && response.statusCode < 300) {
      // 请求成功
      return response.data;
    } else {
      // 处理错误
      handleError(response);
      return response?.data ?? {};
    }
  } catch (error) {
    // 错误处理
    handleError(error);
    return error;
  }
};

const handleError = (error: any) => {
  let errorMessage = '请求失败，请稍后重试';
  if (error && error.statusCode) {
    // 根据实际API返回的错误信息进行调整
    errorMessage = `请求错误 ${error.statusCode}`;
  }
  Taro.showToast({
    title: errorMessage,
    icon: 'none',
    duration: 2000,
  });
  throw new Error(errorMessage);
};

export default request;
