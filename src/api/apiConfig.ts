import { TOKEN_NAME } from '@/utils/constant';
import Taro from '@tarojs/taro';
import { _localStorage, toUrl } from '@/utils';

interface RequestOptions {
  path: string;
  method?: keyof Taro.request.Method;
  type?: any;
  body?: any;
  query?: any;
  format?: any;
  secure?: boolean;
}

// Token管理
const getToken = () => {
  const token = _localStorage.getItem(TOKEN_NAME);
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// 检查token是否过期
const isTokenExpired = () => {
  const accessTokenTimeOut = _localStorage.getItem('accessTokenTimeOut');
  if (!accessTokenTimeOut) return true;

  const expireTime = new Date(accessTokenTimeOut).getTime();
  const currentTime = new Date().getTime();

  // 提前5分钟刷新token
  return currentTime >= expireTime - 5 * 60 * 1000;
};

// 检查refresh token是否过期
const isRefreshTokenExpired = () => {
  const refreshTokenTimeOut = _localStorage.getItem('refreshTokenTimeOut');
  if (!refreshTokenTimeOut) return true;

  const expireTime = new Date(refreshTokenTimeOut).getTime();
  const currentTime = new Date().getTime();

  return currentTime >= expireTime;
};

// 刷新token
const refreshToken = async () => {
  try {
    const refreshTokenValue = _localStorage.getItem('refreshToken');
    const uid = _localStorage.getItem('uid');

    if (!refreshTokenValue || !uid || isRefreshTokenExpired()) {
      throw new Error('Refresh token无效或已过期');
    }

    const response = await Taro.request({
      url: SERVER_URL + '/common/v1.0/refresh',
      method: 'POST',
      data: {
        refreshToken: refreshTokenValue,
        uid: uid,
      },
      header: {
        'content-type': 'application/json',
        appSign: 'HXGW',
      },
    });

    if (response.statusCode === 200 && response.data?.success && response.data?.data) {
      const {
        accessToken,
        refreshToken: newRefreshToken,
        accessTokenTimeOut,
        refreshTokenTimeOut,
      } = response.data.data;

      // 更新存储的token信息
      _localStorage.setItem(TOKEN_NAME, accessToken);
      _localStorage.setItem('refreshToken', newRefreshToken);
      _localStorage.setItem('accessTokenTimeOut', accessTokenTimeOut);
      _localStorage.setItem('refreshTokenTimeOut', refreshTokenTimeOut);

      return true;
    } else {
      throw new Error('刷新token失败');
    }
  } catch (error) {
    console.error('刷新token失败:', error);
    // 清空所有token信息
    clearTokens();
    // 跳转到登录页
    Taro.redirectTo({ url: '/pages/login/index' });
    return false;
  }
};

// 清空token信息
const clearTokens = () => {
  _localStorage.removeItem(TOKEN_NAME);
  _localStorage.removeItem('refreshToken');
  _localStorage.removeItem('uid');
  _localStorage.removeItem('accessTokenTimeOut');
  _localStorage.removeItem('refreshTokenTimeOut');
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const request = async <T = any, _E = any>(
  options: RequestOptions & {
    isloading?: boolean;
  }
): Promise<T> => {
  // 设置请求默认值
  const { path, method = 'GET', body, query, type, isloading = false, ...params } = options;

  try {
    // 请求拦截器
    isloading && Taro.showLoading({ title: '加载中...' });

    // 发起请求
    const response = await Taro.request({
      url: SERVER_URL + path, // 请求地址
      method: method, // 请求方法
      data: body || query, // 发送的数据
      header: {
        'content-type': type || 'application/json', // 默认头部
        appSign: 'HXGW',
        ...getToken(),
        ...((params as any)?.header ?? {}), // 自定义头部
      },
      ...params,
    });

    isloading && Taro.hideLoading();

    // 处理返回
    if (response.statusCode >= 200 && response.statusCode < 300) {
      // 请求成功
      return response.data;
    } else {
      // 处理错误
      handleError(response);
      return response?.data ?? {};
    }
  } catch (error) {
    // 错误处理
    handleError(error);
    return error;
  }
};

const handleError = (error: any) => {
  console.log(error);
  let errorMessage = '请求失败，请稍后重试';
  if (error && error.statusCode) {
    if (error.statusCode === 401) {
      errorMessage = `登录失效，请重新登录`;
      clearTokens();
      toUrl('/pages/login/index');
    } else {
      errorMessage = `请求错误 ${error.statusCode}`;
    }
  }
  Taro.showToast({
    title: errorMessage,
    icon: 'none',
    duration: 2000,
  });
  throw new Error(errorMessage);
};

export default request;
