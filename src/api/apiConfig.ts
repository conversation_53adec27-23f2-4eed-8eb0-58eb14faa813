import { TOKEN_NAME } from '@/utils/constant';
import Taro from '@tarojs/taro';
import { _localStorage } from '@/utils';

interface RequestOptions {
  path: string;
  method?: keyof Taro.request.Method;
  type?: any;
  body?: any;
  query?: any;
  format?: any;
  secure?: boolean;
}

// 检查token是否过期
const isTokenExpired = () => {
  const accessTokenTimeOut = _localStorage.getItem('accessTokenTimeOut');
  if (!accessTokenTimeOut) return true;

  const expireTime = new Date(accessTokenTimeOut).getTime();
  const currentTime = new Date().getTime();

  // 提前5分钟判断为过期，给刷新留出时间
  return currentTime >= (expireTime - 5 * 60 * 1000);
};

// 检查refreshToken是否过期
const isRefreshTokenExpired = () => {
  const refreshTokenTimeOut = _localStorage.getItem('refreshTokenTimeOut');
  if (!refreshTokenTimeOut) return true;

  const expireTime = new Date(refreshTokenTimeOut).getTime();
  const currentTime = new Date().getTime();

  return currentTime >= expireTime;
};

// 获取token头部
const getToken = () => {
  const token = _localStorage.getItem(TOKEN_NAME);
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// 刷新token的函数
let isRefreshing = false;
let refreshPromise: Promise<boolean> | null = null;

const refreshAccessToken = async (): Promise<boolean> => {
  // 如果正在刷新，返回已有的Promise
  if (isRefreshing && refreshPromise) {
    return refreshPromise;
  }

  isRefreshing = true;

  refreshPromise = new Promise(async (resolve) => {
    try {
      const refreshToken = _localStorage.getItem('refreshToken');
      const uid = _localStorage.getItem('uid');

      if (!refreshToken || !uid || isRefreshTokenExpired()) {
        // refreshToken也过期了，需要重新登录
        clearTokens();
        redirectToLogin();
        resolve(false);
        return;
      }

      const response = await Taro.request({
        url: SERVER_URL + '/common/v1.0/refresh',
        method: 'POST',
        data: {
          refreshToken,
          uid
        },
        header: {
          'content-type': 'application/json',
          'appSign': 'HXGW'
        }
      });

      if (response.statusCode === 200 && response.data?.success && response.data?.data) {
        const { accessToken, refreshToken: newRefreshToken, accessTokenTimeOut, refreshTokenTimeOut } = response.data.data;

        // 更新存储的token信息
        _localStorage.setItem(TOKEN_NAME, accessToken);
        _localStorage.setItem('refreshToken', newRefreshToken);
        _localStorage.setItem('accessTokenTimeOut', accessTokenTimeOut);
        _localStorage.setItem('refreshTokenTimeOut', refreshTokenTimeOut);

        resolve(true);
      } else {
        // 刷新失败，清空所有token
        clearTokens();
        redirectToLogin();
        resolve(false);
      }
    } catch (error) {
      console.error('刷新token失败:', error);
      clearTokens();
      redirectToLogin();
      resolve(false);
    } finally {
      isRefreshing = false;
      refreshPromise = null;
    }
  });

  return refreshPromise;
};

// 清空token信息
const clearTokens = () => {
  _localStorage.removeItem(TOKEN_NAME);
  _localStorage.removeItem('refreshToken');
  _localStorage.removeItem('uid');
  _localStorage.removeItem('accessTokenTimeOut');
  _localStorage.removeItem('refreshTokenTimeOut');
};

// 跳转到登录页
const redirectToLogin = () => {
  Taro.showToast({
    title: '登录已过期，请重新登录',
    icon: 'none',
    duration: 2000
  });

  setTimeout(() => {
    Taro.redirectTo({ url: '/pages/login/index' });
  }, 2000);
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const request = async <T = any, _E = any>(
  options: RequestOptions & {
    isloading?: boolean;
    skipAuth?: boolean; // 跳过身份验证，用于登录、注册等接口
  }
): Promise<T> => {
  // 设置请求默认值
  const { path, method = 'GET', body, query, type, isloading = false, skipAuth = false, ...params } = options;

  try {
    // 请求拦截器
    isloading && Taro.showLoading({ title: '加载中...' });

    // 如果不跳过身份验证，检查token状态
    if (!skipAuth) {
      const token = _localStorage.getItem(TOKEN_NAME);

      // 如果没有token，跳转到登录页
      if (!token) {
        isloading && Taro.hideLoading();
        redirectToLogin();
        throw new Error('未登录');
      }

      // 如果token过期，尝试刷新
      if (isTokenExpired()) {
        const refreshSuccess = await refreshAccessToken();
        if (!refreshSuccess) {
          isloading && Taro.hideLoading();
          throw new Error('token刷新失败');
        }
      }
    }

    // 发起请求
    const response = await Taro.request({
      url: SERVER_URL + path, // 请求地址
      method: method, // 请求方法
      data: body || query, // 发送的数据
      header: {
        'content-type': type || 'application/json', // 默认头部
        appSign: 'HXGW',
        ...(skipAuth ? {} : getToken()), // 根据是否跳过身份验证来决定是否携带token
        ...((params as any)?.header ?? {}), // 自定义头部
      },
      ...params,
    });

    isloading && Taro.hideLoading();

    // 处理返回
    if (response.statusCode >= 200 && response.statusCode < 300) {
      // 检查是否是401未授权错误
      if (response.data?.code === 401 && !skipAuth) {
        // token无效，尝试刷新
        const refreshSuccess = await refreshAccessToken();
        if (refreshSuccess) {
          // 刷新成功，重新发起请求
          return request(options);
        } else {
          // 刷新失败，跳转到登录页
          throw new Error('身份验证失败');
        }
      }

      // 请求成功
      return response.data;
    } else {
      // 处理错误
      handleError(response);
      return response?.data ?? {};
    }
  } catch (error) {
    isloading && Taro.hideLoading();
    // 错误处理
    handleError(error);
    throw error;
  }
};

const handleError = (error: any) => {
  let errorMessage = '请求失败，请稍后重试';
  if (error && error.statusCode) {
    // 根据实际API返回的错误信息进行调整
    errorMessage = `请求错误 ${error.statusCode}`;
  }
  Taro.showToast({
    title: errorMessage,
    icon: 'none',
    duration: 2000,
  });
  throw new Error(errorMessage);
};

export default request;
